"""
Enhanced FastAPI Backend for MCP Client with Context Retention
Extends the existing main.py with session-aware conversation management
"""

from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import asyncio
import logging
from contextlib import asynccontextmanager
import uvicorn
import os
import uuid

from main import (
    MCPServerConfig, ChatRequest,
    MCPServerConnection, MCPClientManager, DEFAULT_MCP_SERVERS,
    get_executable_name
)

# Import Bedrock session management (updated module path)
from session_manager_new import session_manager
from enhanced_mcp_manager import EnhancedMCPMixin

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class EnhancedMCPClientManager(MCPClientManager, EnhancedMCPMixin):
    """
    Enhanced MCP Client Manager with context retention capabilities.
    """

    def __init__(self):
        super().__init__()
        self.model_id = os.getenv("BEDROCK_MODEL_ID", "anthropic.claude-3-5-sonnet-20240620-v1:0")
        self._async_bedrock_client = None
        logger.info("Enhanced MCP Client Manager initialized with context retention")

    async def get_async_bedrock_runtime(self):
        """Provide a pooled async bedrock-runtime client."""
        try:
            import aioboto3
        except ImportError as e:
            raise ImportError("aioboto3 is required for async Bedrock operations. pip install aioboto3") from e

        if self._async_bedrock_client is None:
            session = aioboto3.Session()
            self._async_bedrock_client = await session.client("bedrock-runtime").__aenter__()
        return self._async_bedrock_client


class EnhancedChatResponse(BaseModel):
    response: str
    conversation_id: str
    tools_used: List[Dict[str, Any]] = []
    status: str = "success"
    session_stats: Optional[Dict[str, Any]] = None
    context_used: bool = False


enhanced_mcp_manager = EnhancedMCPClientManager()

# Global variable to track configuration status
_server_config_status = {
    "started": False,
    "completed": False,
    "in_progress": False,
    "servers_configured": 0,
    "total_servers": len(DEFAULT_MCP_SERVERS) if 'DEFAULT_MCP_SERVERS' in globals() else 0,
    "errors": []
}


async def session_monitor_worker():
    """Background worker to monitor Bedrock session health."""
    try:
        while True:
            try:
                stats = session_manager.get_all_sessions_stats()
                active_count = stats.get("active_sessions", 0)
                if active_count > 0:
                    logger.info(f"Active Bedrock sessions: {active_count}")
                await asyncio.sleep(300)
            except asyncio.CancelledError:
                logger.info("Session monitor worker cancelled")
                break
            except Exception as e:
                logger.error(f"Session monitor error: {e}")
                await asyncio.sleep(60)
    except asyncio.CancelledError:
        logger.info("Session monitor worker cancelled")
    except Exception as e:
        logger.error(f"Session monitor worker error: {e}")


async def configure_servers_background():
    """Background task to configure MCP servers after startup."""
    global _server_config_status

    logger.info("Starting background MCP server configuration...")
    _server_config_status.update({
        "started": True,
        "in_progress": True,
        "total_servers": len(DEFAULT_MCP_SERVERS)
    })

    async def configure_server_safe(server_config: MCPServerConfig):
        if not server_config.enabled:
            return False, f"Server {server_config.name} is disabled"
        logger.info(f"Adding server: {server_config.name}")
        max_retries = 2 if server_config.name in ["cost-explorer", "aws-pricing"] else 1
        for attempt in range(max_retries):
            try:
                if attempt > 0:
                    logger.info(f"Retry attempt {attempt + 1} for {server_config.name}")
                    await asyncio.sleep(1.0)  # Reduced sleep time
                success = await enhanced_mcp_manager.add_server(server_config)
                if success:
                    logger.info(f"✅ Successfully configured {server_config.name}")
                    _server_config_status["servers_configured"] += 1
                    return True, None
                else:
                    connection = enhanced_mcp_manager.connections.get(server_config.name)
                    error_msg = connection.error if connection else "Unknown error"
                    if "timeout" not in error_msg.lower() or attempt == max_retries - 1:
                        logger.error(f"❌ Failed to configure {server_config.name}: {error_msg}")
                        _server_config_status["errors"].append(f"{server_config.name}: {error_msg}")
                        return False, error_msg
                    else:
                        logger.warning(f"⚠️ Timeout for {server_config.name}, will retry...")
            except asyncio.CancelledError:
                logger.info(f"Configuration cancelled for {server_config.name}")
                return False, "Cancelled"
            except Exception as e:
                if attempt == max_retries - 1:
                    logger.error(f"❌ Error configuring {server_config.name}: {e}")
                    _server_config_status["errors"].append(f"{server_config.name}: {str(e)}")
                    return False, str(e)
                else:
                    logger.warning(f"⚠️ Exception for {server_config.name}, will retry: {e}")
        return False, "Max retries exceeded"

    try:
        results = []
        for config in DEFAULT_MCP_SERVERS:
            logger.info(f"Configuring server: {config.name}")
            result = await configure_server_safe(config)
            results.append(result)
            await asyncio.sleep(0.5)  # Reduced sleep time

        # Count only successful tuples (True, None)
        successful_count = sum(1 for res in results if isinstance(res, tuple) and res[0])
        logger.info(f"🚀 Background server configuration complete: {successful_count}/{len(DEFAULT_MCP_SERVERS)} servers connected")

        _server_config_status.update({
            "completed": True,
            "in_progress": False,
            "servers_configured": successful_count
        })

        if successful_count == 0:
            logger.warning("⚠️ No MCP servers connected")
    except asyncio.CancelledError:
        logger.info("Background server configuration cancelled")
        _server_config_status.update({
            "completed": True,
            "in_progress": False
        })
    except Exception as e:
        logger.error(f"Error in background server configuration: {e}")
        _server_config_status.update({
            "completed": True,
            "in_progress": False,
            "errors": _server_config_status["errors"] + [f"General error: {str(e)}"]
        })


@asynccontextmanager
async def enhanced_lifespan(app: FastAPI):
    """Enhanced lifespan with session cleanup and fast startup."""
    logger.info("Starting Enhanced MCP Client API with Context Retention")
    monitor_task = asyncio.create_task(session_monitor_worker())

    # Start server configuration in background to avoid blocking startup
    config_task = None
    if os.getenv("AUTO_CONFIGURE_SERVERS", "true").lower() == "true":
        logger.info("Starting background MCP server configuration...")
        config_task = asyncio.create_task(configure_servers_background())

    try:
        yield
    finally:
        logger.info("Shutting down Enhanced MCP Client API")

        # Cancel background tasks
        if config_task and not config_task.done():
            config_task.cancel()
            try:
                await config_task
            except asyncio.CancelledError:
                pass

        monitor_task.cancel()
        try:
            await monitor_task
        except asyncio.CancelledError:
            pass

        try:
            await enhanced_mcp_manager.cleanup()
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")


app = FastAPI(
    title="Enhanced MCP Client API with Context Retention",
    description="Multi-server MCP client with Bedrock integration and conversation context",
    version="2.0.0",
    lifespan=enhanced_lifespan
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/")
async def root():
    """Health check endpoint with session stats and server status."""
    session_stats = session_manager.get_all_sessions_stats()

    # Get server connection status
    server_status = {}
    total_servers = len(DEFAULT_MCP_SERVERS)
    connected_servers = 0

    for name, connection in enhanced_mcp_manager.connections.items():
        server_status[name] = {
            "status": connection.status,
            "tools_count": len(connection.tools),
            "error": connection.error
        }
        if connection.status == "connected":
            connected_servers += 1

    return {
        "message": "Enhanced MCP Client API with Context Retention is running",
        "status": "healthy",
        "features": ["context_retention", "session_management", "tool_tracking"],
        "session_stats": session_stats,
        "server_stats": {
            "total_servers": total_servers,
            "connected_servers": connected_servers,
            "connection_rate": f"{connected_servers}/{total_servers}",
            "servers": server_status
        }
    }


@app.post("/chat", response_model=EnhancedChatResponse)
async def enhanced_chat_endpoint(request: ChatRequest):
    """Enhanced chat endpoint with context retention."""
    try:
        conversation_id = request.conversation_id or f"conv_{uuid.uuid4().hex[:8]}"
        tools_available = list(enhanced_mcp_manager.get_available_tools().keys()) if request.use_tools else []

        result = await enhanced_mcp_manager.chat_with_bedrock_with_context(
            message=request.message,
            session_id=conversation_id,
            tools_available=tools_available
        )

        chat_session = session_manager.get_session(conversation_id)
        session_stats = chat_session.get_session_stats() if chat_session else {"error": "Session not found"}

        return EnhancedChatResponse(
            response=result["response"],
            conversation_id=conversation_id,
            tools_used=result["tools_used"],
            status="success" if not result.get("error") else "error",
            session_stats=session_stats,
            context_used=session_stats.get('total_turns', 0) > 0 if session_stats and 'error' not in session_stats else False
        )
    except Exception as e:
        logger.error(f"Enhanced chat error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/sessions/{session_id}/history")
async def get_session_history(session_id: str):
    """Get conversation history for a session."""
    chat_session = session_manager.get_session(session_id)
    if not chat_session:
        raise HTTPException(status_code=404, detail="Session not found")
    session_stats = chat_session.get_session_stats()
    history = [turn.to_dict() for turn in chat_session.conversation_history]
    return {
        "session_id": session_id,
        "created_at": session_stats.get('created_at'),
        "last_activity": session_stats.get('last_activity'),
        "message_count": len(history),
        "total_tools_used": session_stats.get('total_tools_used', 0),
        "history": history
    }


@app.get("/sessions/{session_id}/stats")
async def get_session_stats(session_id: str):
    """Get detailed statistics for a session."""
    chat_session = session_manager.get_session(session_id)
    if not chat_session:
        raise HTTPException(status_code=404, detail="Session not found")
    return chat_session.get_session_stats()


@app.delete("/sessions/{session_id}")
async def clear_session(session_id: str):
    """Delete a Bedrock session."""
    try:
        session_manager.delete_session(session_id)
        return {"message": f"Session {session_id} deleted successfully"}
    except Exception as e:
        logger.error(f"Error deleting session: {e}")
        raise HTTPException(status_code=404, detail="Session not found or could not be deleted")


@app.get("/sessions")
async def list_sessions():
    """List all active sessions with basic stats."""
    sessions_info = {}
    for sid, chat_session in session_manager.sessions.items():
        try:
            session_stats = chat_session.get_session_stats()
            sessions_info[sid] = session_stats
        except Exception as e:
            logger.warning(f"Could not get stats for session {sid}: {e}")
    return {
        "sessions": sessions_info,
        "total_sessions": len(sessions_info),
        "global_stats": session_manager.get_all_sessions_stats()
    }


@app.post("/sessions/cleanup")
async def manual_session_cleanup():
    """Manually trigger session cleanup."""
    cleaned_count = session_manager.cleanup_expired_sessions()
    return {
        "message": f"Cleaned up {cleaned_count} expired sessions",
        "remaining_sessions": len(session_manager.sessions),
        "global_stats": session_manager.get_all_sessions_stats()
    }


@app.get("/servers")
async def list_servers():
    """List all configured MCP servers."""
    servers_info = {}
    for name, connection in enhanced_mcp_manager.connections.items():
        servers_info[name] = {
            "name": name,
            "status": connection.status,
            "tools_count": len(connection.tools),
            "resources_count": len(connection.resources),
            "description": connection.config.description,
            "enabled": connection.config.enabled,
            "error": connection.error
        }
    return servers_info


@app.post("/servers")
async def add_server(config: MCPServerConfig):
    """Add a new MCP server."""
    success = await enhanced_mcp_manager.add_server(config)
    if success:
        return {"message": f"Server {config.name} added successfully"}
    else:
        connection = enhanced_mcp_manager.connections.get(config.name)
        error_msg = connection.error if connection else "Unknown error"
        raise HTTPException(status_code=400, detail=f"Failed to add server {config.name}: {error_msg}")


@app.get("/tools")
async def list_tools():
    """List all available tools across all servers."""
    tools = {}
    available_tools = enhanced_mcp_manager.get_available_tools()
    for tool_key, tool_data in available_tools.items():
        tools[tool_key] = {
            "server": tool_data["server"],
            "name": tool_data["tool"]["name"],
            "description": tool_data["tool"]["description"],
            "input_schema": tool_data["tool"].get("input_schema", {})
        }
    return tools


@app.post("/tools/call")
async def call_tool_endpoint(server_name: str, tool_name: str, arguments: Dict[str, Any]):
    """Call a specific tool."""
    result = await enhanced_mcp_manager.call_tool(server_name, tool_name, arguments)
    if result["success"]:
        return result
    else:
        raise HTTPException(status_code=400, detail=result["error"])


if __name__ == "__main__":
    uvicorn.run(
        "main_enhanced:app",
        host=os.getenv("API_HOST", "0.0.0.0"),
        port=int(os.getenv("API_PORT", "8000")),
        reload=True,
        log_level=os.getenv("LOG_LEVEL", "info")
    )
